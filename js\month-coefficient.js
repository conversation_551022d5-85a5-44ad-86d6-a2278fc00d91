/**
 * month-coefficient.js
 * 版本: 1.0.0
 * 功能: 根据用户选择的品牌、商圈类型和日期计算月系数
 * 更新: 1.0.0 - 初始版本
 */

// 存储月系数数据
let monthCoefficientData = [];

// 月份名称映射
const monthNames = {
  0: '1月',
  1: '2月',
  2: '3月',
  3: '4月',
  4: '5月',
  5: '6月',
  6: '7月',
  7: '8月',
  8: '9月',
  9: '10月',
  10: '11月',
  11: '12月'
};

/**
 * 初始化月系数功能
 */
function initMonthCoefficient() {
  try {
    // 监听日期时间变化
    document.addEventListener('datetimeChanged', function(e) {
      if (e.detail && e.detail.dateTime) {
        updateMonthCoefficient(e.detail.dateTime);
      }
    });
    
    // 监听品牌变化
    const brandSelect = document.getElementById('brand');
    if (brandSelect) {
      brandSelect.addEventListener('change', function() {
        // 如果已经有日期时间，则更新月系数
        if (typeof getSelectedDateTime === 'function') {
          const dateTime = getSelectedDateTime();
          if (dateTime) {
            updateMonthCoefficient(dateTime);
          }
        }
      });
    }
    
    // 监听商圈类型变化
    const areaTypeSelect = document.getElementById('areaType');
    if (areaTypeSelect) {
      areaTypeSelect.addEventListener('change', function() {
        // 如果已经有日期时间，则更新月系数
        if (typeof getSelectedDateTime === 'function') {
          const dateTime = getSelectedDateTime();
          if (dateTime) {
            updateMonthCoefficient(dateTime);
          }
        }
      });
    }
  } catch (error) {
    console.error('初始化月系数功能时出错:', error);
  }
}

/**
 * 处理Excel文件中的月系数数据
 * @param {Object} workbook - XLSX工作簿对象
 * @returns {boolean} 是否成功处理
 */
function processMonthCoefficientData(workbook) {
  try {
    // 查找"月系数"工作表
    const monthCoefficientSheet = workbook.Sheets['月系数'];
    if (!monthCoefficientSheet) {
      console.warn('未找到"月系数"工作表');
      return false;
    }
    
    // 转换为JSON
    monthCoefficientData = XLSX.utils.sheet_to_json(monthCoefficientSheet);
    
    // 确保数据格式正确
    if (!monthCoefficientData.length || !monthCoefficientData[0]['品牌'] || !monthCoefficientData[0]['商圈类型']) {
      console.warn('月系数数据格式不正确');
      return false;
    }
    
    // 验证月份列是否存在
    const firstRow = monthCoefficientData[0];
    let hasAllMonths = true;
    for (let month = 1; month <= 12; month++) {
      const monthName = month + '月';
      if (!(monthName in firstRow)) {
        console.warn(`月系数数据缺少${monthName}列`);
        hasAllMonths = false;
      }
    }
    
    if (!hasAllMonths) {
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('处理月系数数据时出错:', error);
    return false;
  }
}

/**
 * 更新月系数显示
 * @param {Date} dateTime - 日期时间对象
 */
function updateMonthCoefficient(dateTime) {
  if (!monthCoefficientData.length) {
    // 如果没有数据，则不更新
    const monthCoefficientElement = document.getElementById('monthCoefficient');
    if (monthCoefficientElement) {
      monthCoefficientElement.textContent = '--';
    }
    return;
  }
  
  try {
    // 获取当前选中的品牌和商圈类型
    const currentBrand = document.getElementById('brand').value;
    const currentAreaType = document.getElementById('areaType').value;
    
    if (!currentBrand || !currentAreaType) {
      // 如果没有选择品牌或商圈类型，则不更新
      const monthCoefficientElement = document.getElementById('monthCoefficient');
      if (monthCoefficientElement) {
        monthCoefficientElement.textContent = '--';
      }
      return;
    }
    
    // 获取月份（0-11）
    const month = dateTime.getMonth();
    const monthName = monthNames[month];
    
    // 更新当前月份显示
    const currentMonthElement = document.getElementById('currentMonth');
    if (currentMonthElement) {
      currentMonthElement.textContent = monthName;
    }
    
    // 获取当月天数
    const daysInMonth = new Date(dateTime.getFullYear(), month + 1, 0).getDate();
    const daysInMonthElement = document.getElementById('daysInMonth');
    if (daysInMonthElement) {
      daysInMonthElement.textContent = daysInMonth;
    }
    
    // 查找匹配的行
    const row = monthCoefficientData.find(item => 
      item['品牌'] === currentBrand && item['商圈类型'] === currentAreaType
    );
    
    // 更新显示
    const monthCoefficientElement = document.getElementById('monthCoefficient');
    if (monthCoefficientElement) {
      if (row && monthName in row) {
        const coefficient = row[monthName];
        // 如果是数字，转换为百分比显示
        if (typeof coefficient === 'number') {
          monthCoefficientElement.textContent = (coefficient * 100).toFixed(0) + '%';
        } else {
          monthCoefficientElement.textContent = coefficient || '--';
        }
      } else {
        monthCoefficientElement.textContent = '--';
      }
    }
  } catch (error) {
    console.error('更新月系数时出错:', error);
    const monthCoefficientElement = document.getElementById('monthCoefficient');
    if (monthCoefficientElement) {
      monthCoefficientElement.textContent = '--';
    }
  }
}

/**
 * 获取月系数
 * @param {Date} dateTime - 日期时间对象
 * @param {string} brand - 品牌
 * @param {string} areaType - 商圈类型
 * @returns {number|string} 月系数
 */
function getMonthCoefficient(dateTime, brand, areaType) {
  if (!dateTime || !brand || !areaType || !monthCoefficientData.length) {
    return '';
  }
  
  try {
    // 获取月份（0-11）
    const month = dateTime.getMonth();
    const monthName = monthNames[month];
    
    // 查找匹配的行
    const row = monthCoefficientData.find(item => 
      item['品牌'] === brand && item['商圈类型'] === areaType
    );
    
    if (row && monthName in row) {
      return row[monthName];
    }
  } catch (error) {
    console.error('获取月系数时出错:', error);
  }
  
  return '';
}

// 导出函数
window.initMonthCoefficient = initMonthCoefficient;
window.processMonthCoefficientData = processMonthCoefficientData;
window.getMonthCoefficient = getMonthCoefficient; 