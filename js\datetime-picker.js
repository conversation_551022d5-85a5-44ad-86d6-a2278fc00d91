/**
 * datetime-picker.js
 * 版本: 1.0.2
 * 功能: 实现日期时间选择器，替代从Excel获取当前时段
 * 更新: 
 * - 1.0.1: 添加获取当前月份和月天数的功能
 * - 1.0.2: 添加时间变化事件的触发
 */

// 存储当前选择的日期时间
let selectedDateTime = null;

/**
 * 初始化日期时间选择器
 */
function initDateTimePicker() {
  // 获取时间选择按钮元素
  const timeButton = document.getElementById('timeButton');
  if (!timeButton) return;
  
  // 为按钮添加点击事件
  timeButton.addEventListener('click', showDateTimePicker);
  
  // 初始化时设置当前时间
  updateSelectedTime(new Date());
}

/**
 * 显示日期时间选择器弹窗
 */
function showDateTimePicker() {
  // 创建弹窗背景
  const overlay = document.createElement('div');
  overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
  overlay.id = 'datetimeOverlay';
  
  // 获取当前日期时间，如果已选择则使用已选择的
  const now = selectedDateTime || new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // 月份从0开始
  const day = now.getDate();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const seconds = now.getSeconds();
  
  // 创建弹窗内容
  overlay.innerHTML = `
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
      <h3 class="text-xl font-bold mb-4">选择日期和时间</h3>
      
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">年</label>
          <select id="yearSelect" class="border p-2 rounded w-full">
            ${generateYearOptions(year)}
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">月</label>
          <select id="monthSelect" class="border p-2 rounded w-full">
            ${generateMonthOptions(month)}
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">日</label>
          <select id="daySelect" class="border p-2 rounded w-full">
            ${generateDayOptions(year, month, day)}
          </select>
        </div>
      </div>
      
      <div class="grid grid-cols-3 gap-4 mb-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">时</label>
          <select id="hourSelect" class="border p-2 rounded w-full">
            ${generateHourOptions(hours)}
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">分</label>
          <select id="minuteSelect" class="border p-2 rounded w-full">
            ${generateMinuteOptions(minutes)}
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">秒</label>
          <select id="secondSelect" class="border p-2 rounded w-full">
            ${generateSecondOptions(seconds)}
          </select>
        </div>
      </div>
      
      <div class="flex justify-end space-x-2">
        <button id="cancelDateTime" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded">取消</button>
        <button id="confirmDateTime" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">确定</button>
      </div>
    </div>
  `;
  
  // 添加到页面
  document.body.appendChild(overlay);
  
  // 添加事件监听器
  document.getElementById('yearSelect').addEventListener('change', updateDays);
  document.getElementById('monthSelect').addEventListener('change', updateDays);
  document.getElementById('cancelDateTime').addEventListener('click', closeDateTimePicker);
  document.getElementById('confirmDateTime').addEventListener('click', confirmDateTime);
  
  // 阻止点击事件冒泡
  overlay.addEventListener('click', function(e) {
    if (e.target === overlay) {
      closeDateTimePicker();
    }
  });
}

/**
 * 关闭日期时间选择器
 */
function closeDateTimePicker() {
  const overlay = document.getElementById('datetimeOverlay');
  if (overlay) {
    document.body.removeChild(overlay);
  }
}

/**
 * 确认所选日期时间
 */
function confirmDateTime() {
  // 获取所选值
  const year = parseInt(document.getElementById('yearSelect').value);
  const month = parseInt(document.getElementById('monthSelect').value) - 1; // 月份从0开始
  const day = parseInt(document.getElementById('daySelect').value);
  const hour = parseInt(document.getElementById('hourSelect').value);
  const minute = parseInt(document.getElementById('minuteSelect').value);
  const second = parseInt(document.getElementById('secondSelect').value);
  
  // 创建日期对象
  const dateTime = new Date(year, month, day, hour, minute, second);
  
  // 更新选中的时间
  updateSelectedTime(dateTime);
  
  // 关闭弹窗
  closeDateTimePicker();
}

/**
 * 更新选中的时间
 * @param {Date} dateTime - 日期时间对象
 */
function updateSelectedTime(dateTime) {
  selectedDateTime = dateTime;
  
  // 格式化日期时间为字符串
  const formattedDateTime = formatDateTime(dateTime);
  
  // 更新显示
  const timeButton = document.getElementById('timeButton');
  if (timeButton) {
    timeButton.textContent = formattedDateTime;
    timeButton.dataset.datetime = dateTime.toISOString();
  }
  
  // 更新月份信息
  updateMonthInfo(dateTime);
  
  // 触发时间变化事件
  triggerDateTimeChangedEvent(dateTime);
}

/**
 * 触发时间变化事件
 * @param {Date} dateTime - 日期时间对象
 */
function triggerDateTimeChangedEvent(dateTime) {
  const event = new CustomEvent('datetimeChanged', {
    detail: {
      dateTime: dateTime
    },
    bubbles: true,
    cancelable: true
  });
  
  document.dispatchEvent(event);
}

/**
 * 更新月份信息
 * @param {Date} dateTime - 日期时间对象
 */
function updateMonthInfo(dateTime) {
  // 获取月份（1-12）
  const month = dateTime.getMonth() + 1;
  
  // 获取月天数
  const daysInMonth = new Date(dateTime.getFullYear(), month, 0).getDate();
  
  // 更新显示
  const currentMonthElement = document.getElementById('currentMonth');
  const daysInMonthElement = document.getElementById('daysInMonth');
  
  if (currentMonthElement) {
    currentMonthElement.textContent = month;
  }
  
  if (daysInMonthElement) {
    daysInMonthElement.textContent = daysInMonth;
  }
}

/**
 * 格式化日期时间为字符串
 * @param {Date} dateTime - 日期时间对象
 * @returns {string} 格式化后的字符串
 */
function formatDateTime(dateTime) {
  const year = dateTime.getFullYear();
  const month = String(dateTime.getMonth() + 1).padStart(2, '0');
  const day = String(dateTime.getDate()).padStart(2, '0');
  const hours = String(dateTime.getHours()).padStart(2, '0');
  const minutes = String(dateTime.getMinutes()).padStart(2, '0');
  const seconds = String(dateTime.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 生成年份选项
 * @param {number} selectedYear - 当前选中的年份
 * @returns {string} 选项HTML
 */
function generateYearOptions(selectedYear) {
  const currentYear = new Date().getFullYear();
  let options = '';
  
  // 生成从当前年份往前10年、往后10年的选项
  for (let year = currentYear - 10; year <= currentYear + 10; year++) {
    options += `<option value="${year}" ${year === selectedYear ? 'selected' : ''}>${year}</option>`;
  }
  
  return options;
}

/**
 * 生成月份选项
 * @param {number} selectedMonth - 当前选中的月份
 * @returns {string} 选项HTML
 */
function generateMonthOptions(selectedMonth) {
  let options = '';
  
  for (let month = 1; month <= 12; month++) {
    options += `<option value="${month}" ${month === selectedMonth ? 'selected' : ''}>${String(month).padStart(2, '0')}</option>`;
  }
  
  return options;
}

/**
 * 生成日期选项
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} selectedDay - 当前选中的日期
 * @returns {string} 选项HTML
 */
function generateDayOptions(year, month, selectedDay) {
  let options = '';
  
  // 获取指定年月的天数
  const daysInMonth = new Date(year, month, 0).getDate();
  
  for (let day = 1; day <= daysInMonth; day++) {
    options += `<option value="${day}" ${day === selectedDay ? 'selected' : ''}>${String(day).padStart(2, '0')}</option>`;
  }
  
  return options;
}

/**
 * 生成小时选项
 * @param {number} selectedHour - 当前选中的小时
 * @returns {string} 选项HTML
 */
function generateHourOptions(selectedHour) {
  let options = '';
  
  for (let hour = 0; hour < 24; hour++) {
    options += `<option value="${hour}" ${hour === selectedHour ? 'selected' : ''}>${String(hour).padStart(2, '0')}</option>`;
  }
  
  return options;
}

/**
 * 生成分钟选项
 * @param {number} selectedMinute - 当前选中的分钟
 * @returns {string} 选项HTML
 */
function generateMinuteOptions(selectedMinute) {
  let options = '';
  
  for (let minute = 0; minute < 60; minute++) {
    options += `<option value="${minute}" ${minute === selectedMinute ? 'selected' : ''}>${String(minute).padStart(2, '0')}</option>`;
  }
  
  return options;
}

/**
 * 生成秒数选项
 * @param {number} selectedSecond - 当前选中的秒数
 * @returns {string} 选项HTML
 */
function generateSecondOptions(selectedSecond) {
  let options = '';
  
  for (let second = 0; second < 60; second++) {
    options += `<option value="${second}" ${second === selectedSecond ? 'selected' : ''}>${String(second).padStart(2, '0')}</option>`;
  }
  
  return options;
}

/**
 * 更新日期选项
 */
function updateDays() {
  const yearSelect = document.getElementById('yearSelect');
  const monthSelect = document.getElementById('monthSelect');
  const daySelect = document.getElementById('daySelect');
  
  if (!yearSelect || !monthSelect || !daySelect) return;
  
  const year = parseInt(yearSelect.value);
  const month = parseInt(monthSelect.value);
  const currentDay = parseInt(daySelect.value);
  
  // 获取指定年月的天数
  const daysInMonth = new Date(year, month, 0).getDate();
  
  // 保存当前选择的日期
  let selectedDay = currentDay;
  if (selectedDay > daysInMonth) {
    selectedDay = daysInMonth;
  }
  
  // 重新生成日期选项
  daySelect.innerHTML = generateDayOptions(year, month, selectedDay);
}

/**
 * 获取当前选择的日期时间
 * @returns {Date|null} 日期时间对象
 */
function getSelectedDateTime() {
  return selectedDateTime;
}

/**
 * 获取当前月份（1-12）
 * @returns {number} 月份
 */
function getCurrentMonth() {
  return selectedDateTime ? selectedDateTime.getMonth() + 1 : new Date().getMonth() + 1;
}

/**
 * 获取当前月天数
 * @returns {number} 天数
 */
function getDaysInMonth() {
  if (!selectedDateTime) {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
  }
  
  return new Date(selectedDateTime.getFullYear(), selectedDateTime.getMonth() + 1, 0).getDate();
}

// 导出函数
window.initDateTimePicker = initDateTimePicker;
window.getSelectedDateTime = getSelectedDateTime;
window.getCurrentMonth = getCurrentMonth;
window.getDaysInMonth = getDaysInMonth; 