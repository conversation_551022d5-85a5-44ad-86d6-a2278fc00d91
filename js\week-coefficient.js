/**
 * week-coefficient.js
 * 版本: 1.0.1
 * 功能: 根据用户选择的品牌、商圈类型和时间计算周系数
 * 更新: 1.0.1 - 修复变量重复声明问题
 */

// 存储周系数数据
let weekCoefficientData = [];
// 当前选中的品牌和商圈类型（从time-period.js中共享）
// 星期几对应的列索引映射
const dayOfWeekColumns = {
  1: '星期一', // 周一
  2: '星期二', // 周二
  3: '星期三', // 周三
  4: '星期四', // 周四
  5: '星期五', // 周五
  6: '星期六', // 周六
  0: '星期日'  // 周日
};

/**
 * 初始化周系数功能
 */
function initWeekCoefficient() {
  // 监听日期时间变化
  document.addEventListener('datetimeChanged', function(e) {
    if (e.detail && e.detail.dateTime) {
      updateWeekCoefficient(e.detail.dateTime);
    }
  });
  
  // 监听品牌变化
  const brandSelect = document.getElementById('brand');
  if (brandSelect) {
    brandSelect.addEventListener('change', function() {
      // 如果已经有日期时间，则更新周系数
      if (typeof getSelectedDateTime === 'function') {
        const dateTime = getSelectedDateTime();
        if (dateTime) {
          updateWeekCoefficient(dateTime);
        }
      }
    });
  }
  
  // 监听商圈类型变化
  const areaTypeSelect = document.getElementById('areaType');
  if (areaTypeSelect) {
    areaTypeSelect.addEventListener('change', function() {
      // 如果已经有日期时间，则更新周系数
      if (typeof getSelectedDateTime === 'function') {
        const dateTime = getSelectedDateTime();
        if (dateTime) {
          updateWeekCoefficient(dateTime);
        }
      }
    });
  }
}

/**
 * 处理Excel文件中的周系数数据
 * @param {Object} workbook - XLSX工作簿对象
 * @returns {boolean} 是否成功处理
 */
function processWeekCoefficientData(workbook) {
  try {
    // 查找"周系数"工作表
    const weekCoefficientSheet = workbook.Sheets['周系数'];
    if (!weekCoefficientSheet) {
      console.warn('未找到"周系数"工作表');
      return false;
    }
    
    // 转换为JSON
    weekCoefficientData = XLSX.utils.sheet_to_json(weekCoefficientSheet);
    
    // 确保数据格式正确
    if (!weekCoefficientData.length || !weekCoefficientData[0]['品牌'] || !weekCoefficientData[0]['商圈类型']) {
      console.warn('周系数数据格式不正确');
      return false;
    }
    
    // 验证星期列是否存在
    const firstRow = weekCoefficientData[0];
    let hasAllDays = true;
    for (const day in dayOfWeekColumns) {
      const dayName = dayOfWeekColumns[day];
      if (!(dayName in firstRow)) {
        console.warn(`周系数数据缺少${dayName}列`);
        hasAllDays = false;
      }
    }
    
    if (!hasAllDays) {
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('处理周系数数据时出错:', error);
    return false;
  }
}

/**
 * 更新周系数显示
 * @param {Date} dateTime - 日期时间对象
 */
function updateWeekCoefficient(dateTime) {
  if (!weekCoefficientData.length) {
    // 如果没有数据，则不更新
    const weekCoefficientElement = document.getElementById('weekCoefficient');
    if (weekCoefficientElement) {
      weekCoefficientElement.textContent = '--';
    }
    return;
  }
  
  // 获取当前选中的品牌和商圈类型
  const currentBrand = document.getElementById('brand').value;
  const currentAreaType = document.getElementById('areaType').value;
  
  if (!currentBrand || !currentAreaType) {
    // 如果没有选择品牌或商圈类型，则不更新
    const weekCoefficientElement = document.getElementById('weekCoefficient');
    if (weekCoefficientElement) {
      weekCoefficientElement.textContent = '--';
    }
    return;
  }
  
  // 获取星期几（0-6，0表示周日）
  const dayOfWeek = dateTime.getDay();
  const dayName = dayOfWeekColumns[dayOfWeek];
  
  // 查找匹配的行
  const row = weekCoefficientData.find(item => 
    item['品牌'] === currentBrand && item['商圈类型'] === currentAreaType
  );
  
  // 更新显示
  const weekCoefficientElement = document.getElementById('weekCoefficient');
  if (weekCoefficientElement) {
    if (row && dayName in row) {
      const coefficient = row[dayName];
      // 如果是数字，转换为百分比显示
      if (typeof coefficient === 'number') {
        weekCoefficientElement.textContent = (coefficient * 100).toFixed(0) + '%';
      } else {
        weekCoefficientElement.textContent = coefficient || '--';
      }
    } else {
      weekCoefficientElement.textContent = '--';
    }
  }
}

/**
 * 获取周系数
 * @param {Date} dateTime - 日期时间对象
 * @param {string} brand - 品牌
 * @param {string} areaType - 商圈类型
 * @returns {number|string} 周系数
 */
function getWeekCoefficient(dateTime, brand, areaType) {
  if (!dateTime || !brand || !areaType || !weekCoefficientData.length) {
    return '';
  }
  
  // 获取星期几（0-6，0表示周日）
  const dayOfWeek = dateTime.getDay();
  const dayName = dayOfWeekColumns[dayOfWeek];
  
  // 查找匹配的行
  const row = weekCoefficientData.find(item => 
    item['品牌'] === brand && item['商圈类型'] === areaType
  );
  
  if (row && dayName in row) {
    return row[dayName];
  }
  
  return '';
}

// 导出函数
window.initWeekCoefficient = initWeekCoefficient;
window.processWeekCoefficientData = processWeekCoefficientData;
window.getWeekCoefficient = getWeekCoefficient; 