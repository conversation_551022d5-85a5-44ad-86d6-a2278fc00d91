/**
 * excel-handler.js
 * 版本: 1.0.7
 * 功能: 处理Excel文件的导入和数据提取，支持基础信息工作表的数据读取和下拉选择框的联动
 * 更新: 
 * - 1.0.1: 删除测试数据相关功能
 * - 1.0.2: 移除对"当前时段"的处理，改为使用日期时间选择器
 * - 1.0.3: 修改城市等级的处理方式，适应新的显示方式
 * - 1.0.4: 添加对时段系数数据的处理
 * - 1.0.5: 添加对周系数数据的处理
 * - 1.0.6: 添加对月系数数据的处理
 * - 1.0.7: 添加对经营系数数据的处理
 */

// 存储Excel数据的全局变量
let baseInfoData = null;
let cityMapping = {}; // 省份-城市映射
let districtMapping = {}; // 城市-区县映射
let cityLevelMapping = {}; // 区县-城市等级映射

/**
 * 处理Excel文件上传
 * @param {Event} event - 文件上传事件
 */
function handleExcelUpload(event) {
  const file = event.target.files[0];
  if (!file) return;
  
  const reader = new FileReader();
  
  reader.onload = function(e) {
    try {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      
      // 查找"基础信息"工作表
      const baseInfoSheet = workbook.Sheets['基础信息'];
      if (!baseInfoSheet) {
        alert('未找到"基础信息"工作表！');
        return;
      }
      
      // 转换为JSON
      baseInfoData = XLSX.utils.sheet_to_json(baseInfoSheet);
      
      // 处理数据，构建映射关系
      processBaseInfoData();
      
      // 填充下拉选择框
      populateDropdowns();
      
      // 处理时段系数数据
      if (typeof processTimePeriodData === 'function') {
        const result = processTimePeriodData(workbook);
        if (result) {
          // 如果已经选择了时间，则更新时段
          if (getSelectedDateTime) {
            const dateTime = getSelectedDateTime();
            if (dateTime) {
              // 触发时间变化事件，更新时段
              const event = new CustomEvent('datetimeChanged', {
                detail: { dateTime: dateTime },
                bubbles: true,
                cancelable: true
              });
              document.dispatchEvent(event);
            }
          }
        }
      }
      
      // 处理周系数数据
      if (typeof processWeekCoefficientData === 'function') {
        processWeekCoefficientData(workbook);
      }
      
      // 处理月系数数据
      if (typeof processMonthCoefficientData === 'function') {
        processMonthCoefficientData(workbook);
      }
      
      // 处理经营系数数据
      if (typeof processBusinessCoefficientData === 'function') {
        processBusinessCoefficientData(workbook);
      }
      
      alert('配置文件读取成功，已加载基础信息数据');
    } catch (error) {
      console.error('处理Excel文件时出错:', error);
      alert('处理Excel文件时出错: ' + error.message);
    }
  };
  
  reader.onerror = function() {
    alert('读取文件时出错!');
  };
  
  reader.readAsArrayBuffer(file);
}

/**
 * 处理基础信息数据，构建映射关系
 */
function processBaseInfoData() {
  if (!baseInfoData || !baseInfoData.length) return;
  
  // 重置映射
  cityMapping = {};
  districtMapping = {};
  cityLevelMapping = {};
  
  // 构建映射关系
  baseInfoData.forEach(row => {
    const province = row['省份'];
    const city = row['城市'];
    const district = row['区县'];
    const cityLevel = row['城市等级'];
    
    // 省份-城市映射
    if (province) {
      if (!cityMapping[province]) {
        cityMapping[province] = new Set();
      }
      if (city) {
        cityMapping[province].add(city);
      }
    }
    
    // 城市-区县映射
    if (city) {
      if (!districtMapping[city]) {
        districtMapping[city] = new Set();
      }
      if (district) {
        districtMapping[city].add(district);
      }
    }
    
    // 区县-城市等级映射
    if (district && cityLevel) {
      cityLevelMapping[district] = cityLevel;
    }
  });
  
  // 将Set转换为数组
  for (const province in cityMapping) {
    cityMapping[province] = Array.from(cityMapping[province]);
  }
  
  for (const city in districtMapping) {
    districtMapping[city] = Array.from(districtMapping[city]);
  }
}

/**
 * 填充所有下拉选择框
 */
function populateDropdowns() {
  if (!baseInfoData || !baseInfoData.length) return;
  
  // 获取唯一值的辅助函数
  const getUniqueValues = (field) => {
    const values = new Set();
    baseInfoData.forEach(row => {
      if (row[field]) values.add(row[field]);
    });
    return Array.from(values);
  };
  
  // 填充品牌下拉框
  fillDropdown('brand', getUniqueValues('品牌'));
  
  // 填充省份下拉框
  fillDropdown('province', getUniqueValues('省份'));
  
  // 填充商圈类型下拉框
  fillDropdown('areaType', getUniqueValues('商圈类型'));
  
  // 填充订单渠道下拉框
  fillDropdown('orderChannel', getUniqueValues('订单渠道'));
  
  // 注意：不再填充当前时段下拉框，改为使用日期时间选择器
  
  // 设置联动事件
  setupCascadingDropdowns();
}

/**
 * 填充下拉选择框
 * @param {string} id - 选择框ID
 * @param {Array} values - 选项值数组
 */
function fillDropdown(id, values) {
  const dropdown = document.getElementById(id);
  if (!dropdown) return;
  
  // 清空现有选项，只保留第一个默认选项
  dropdown.innerHTML = `<option value="">${dropdown.options[0].text}</option>`;
  
  // 添加新选项
  values.forEach(value => {
    const option = document.createElement('option');
    option.value = value;
    option.textContent = value;
    dropdown.appendChild(option);
  });
}

/**
 * 设置级联下拉框的事件处理
 */
function setupCascadingDropdowns() {
  // 省份变化时更新城市
  document.getElementById('province').addEventListener('change', function() {
    const province = this.value;
    const cities = province ? (cityMapping[province] || []) : [];
    
    fillDropdown('city', cities);
    document.getElementById('city').dispatchEvent(new Event('change'));
  });
  
  // 城市变化时更新区县
  document.getElementById('city').addEventListener('change', function() {
    const city = this.value;
    const districts = city ? (districtMapping[city] || []) : [];
    
    fillDropdown('district', districts);
    document.getElementById('district').dispatchEvent(new Event('change'));
  });
  
  // 区县变化时更新城市等级
  document.getElementById('district').addEventListener('change', function() {
    const district = this.value;
    const cityLevel = district ? cityLevelMapping[district] : '';
    
    // 更新城市等级显示
    const cityLevelElement = document.getElementById('cityLevel');
    if (cityLevelElement) {
      cityLevelElement.textContent = cityLevel || '--';
    }
  });
}

// 导出函数，使其可以在全局范围内使用
window.handleExcelUpload = handleExcelUpload; 