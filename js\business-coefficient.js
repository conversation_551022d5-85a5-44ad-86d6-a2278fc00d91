/**
 * business-coefficient.js
 * 版本: 1.0.1
 * 功能: 根据用户选择的品牌和商圈类型获取经营系数
 * 更新: 1.0.0 - 初始版本
 * 更新: 1.0.1 - 优化指标名称，使其更精准，避免歧义
 */

// 存储经营系数数据
let businessCoefficientData = [];

// 经营系数字段映射
const businessCoefficientFields = {
  '堂食客单价': 'dineUnitPrice',
  '外送客单价': 'deliveryUnitPrice',
  '第三方外送占比参考': 'thirdPartyRatio',
  '线上点餐占比参考': 'onlineOrderRatio',
  '外送年度占比参考值': 'deliveryYearRatio'
};

/**
 * 初始化经营系数功能
 */
function initBusinessCoefficient() {
  try {
    // 监听品牌变化
    const brandSelect = document.getElementById('brand');
    if (brandSelect) {
      brandSelect.addEventListener('change', function() {
        updateBusinessCoefficients();
      });
    }
    
    // 监听商圈类型变化
    const areaTypeSelect = document.getElementById('areaType');
    if (areaTypeSelect) {
      areaTypeSelect.addEventListener('change', function() {
        updateBusinessCoefficients();
      });
    }
  } catch (error) {
    console.error('初始化经营系数功能时出错:', error);
  }
}

/**
 * 处理Excel文件中的经营系数数据
 * @param {Object} workbook - XLSX工作簿对象
 * @returns {boolean} 是否成功处理
 */
function processBusinessCoefficientData(workbook) {
  try {
    // 查找"经营系数"工作表
    const businessCoefficientSheet = workbook.Sheets['经营系数'];
    if (!businessCoefficientSheet) {
      console.warn('未找到"经营系数"工作表');
      return false;
    }
    
    // 转换为JSON
    businessCoefficientData = XLSX.utils.sheet_to_json(businessCoefficientSheet);
    
    // 确保数据格式正确
    if (!businessCoefficientData.length || !businessCoefficientData[0]['品牌'] || !businessCoefficientData[0]['商圈类型']) {
      console.warn('经营系数数据格式不正确');
      return false;
    }
    

    // 如果已经选择了品牌和商圈类型，则更新经营系数
    updateBusinessCoefficients();
    
    return true;
  } catch (error) {
    console.error('处理经营系数数据时出错:', error);
    return false;
  }
}

/**
 * 更新经营系数显示
 */
function updateBusinessCoefficients() {
  if (!businessCoefficientData.length) {
    // 如果没有数据，则不更新
    resetBusinessCoefficientDisplay();
    return;
  }
  
  try {
    // 获取当前选中的品牌和商圈类型
    const currentBrand = document.getElementById('brand').value;
    const currentAreaType = document.getElementById('areaType').value;
    
    if (!currentBrand || !currentAreaType) {
      // 如果没有选择品牌或商圈类型，则不更新
      resetBusinessCoefficientDisplay();
      return;
    }
    
    // 查找匹配的行
    const row = businessCoefficientData.find(item => 
      item['品牌'] === currentBrand && item['商圈类型'] === currentAreaType
    );
    
    if (!row) {
      resetBusinessCoefficientDisplay();
      return;
    }
    
    // 更新各个经营系数显示
    for (const field in businessCoefficientFields) {
      const elementId = businessCoefficientFields[field];
      const element = document.getElementById(elementId);
      
      if (element) {
        const value = row[field];
        if (value !== undefined) {
          // 根据不同字段类型进行格式化
          if (field.includes('客单价')) {
            // 客单价显示为数字，保留2位小数
            element.textContent = typeof value === 'number' ? value.toFixed(2) : value;
          } else if (field.includes('占比')) {
            // 占比显示为百分比
            if (typeof value === 'number') {
              element.textContent = (value * 100).toFixed(0) + '%';
            } else {
              element.textContent = value;
            }
          } else {
            element.textContent = value;
          }
        } else {
          element.textContent = '--';
        }
      }
    }
  } catch (error) {
    console.error('更新经营系数时出错:', error);
    resetBusinessCoefficientDisplay();
  }
}

/**
 * 重置经营系数显示
 */
function resetBusinessCoefficientDisplay() {
  for (const field in businessCoefficientFields) {
    const elementId = businessCoefficientFields[field];
    const element = document.getElementById(elementId);
    if (element) {
      element.textContent = '--';
    }
  }
}

/**
 * 获取特定经营系数
 * @param {string} brand - 品牌
 * @param {string} areaType - 商圈类型
 * @param {string} field - 经营系数字段名
 * @returns {number|string} 经营系数值
 */
function getBusinessCoefficient(brand, areaType, field) {
  if (!brand || !areaType || !field || !businessCoefficientData.length) {
    return '';
  }
  
  try {
    // 查找匹配的行
    const row = businessCoefficientData.find(item => 
      item['品牌'] === brand && item['商圈类型'] === areaType
    );
    
    if (row && field in row) {
      return row[field];
    }
  } catch (error) {
    console.error('获取经营系数时出错:', error);
  }
  
  return '';
}

// 导出函数
window.initBusinessCoefficient = initBusinessCoefficient;
window.processBusinessCoefficientData = processBusinessCoefficientData;
window.getBusinessCoefficient = getBusinessCoefficient;