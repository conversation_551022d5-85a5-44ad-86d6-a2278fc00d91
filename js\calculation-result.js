/**
 * calculation-result.js
 * 版本: 1.2.0
 * 功能: 根据用户输入和系数数据计算营业额测算结果
 * 更新:
 * - 1.0.0: 初始版本
 * - 1.0.1: 修复系数处理逻辑，确保正确处理百分比格式和0值
 * - 1.0.2: 优化测算结果的输出格式，增加千分位分隔符
 * - 1.0.3: 调整测算结果的营业额显示为整数，去掉小数点
 * - 1.0.4: 修改外送月营业额预估的计算公式，去掉除以12的部分
 * - 1.0.5: 增加外送月营业额占比指标，计算公式为：外送月营业额预估/月总营业额预估
 * - 1.0.6: 修复外送年度占比参考值不显示的问题
 * - 1.0.7: 优化指标名称，使其更精准，避免歧义
 * - 1.0.8: 新增堂食月单量预估指标
 * - 1.0.9: 新增外送当日单量指标
 * - 1.1.0: 删除输出指标：堂食当日单量、堂食月单量预估、外送当日单量、外送月单量预估、外送月营业额占比
 * - 1.2.0: 删除计算公式说明区域
 */

/**
 * 初始化测算结果功能
 */
function initCalculationResult() {
  try {
    // 绑定计算按钮事件
    const calculateButton = document.getElementById('calculateButton');
    if (calculateButton) {
      calculateButton.addEventListener('click', calculateResults);
    }
    

  } catch (error) {
    console.error('初始化测算结果功能时出错:', error);
  }
}

/**
 * 计算测算结果
 */
function calculateResults() {
  try {
    // 获取用户输入
    const mobileOrders = parseInt(document.getElementById('mobileOrders').value) || 0;
    const otherOrders = parseInt(document.getElementById('otherOrders').value) || 0;
    const meituanOrders = parseInt(document.getElementById('meituanOrders').value) || 0;
    const elemeOrders = parseInt(document.getElementById('elemeOrders').value) || 0;
    
    // 获取当前选择的品牌、商圈类型和订单渠道
    const brand = document.getElementById('brand').value;
    const areaType = document.getElementById('areaType').value;
    const orderChannel = document.getElementById('orderChannel').value;
    
    // 获取日期时间
    let dateTime = null;
    if (typeof getSelectedDateTime === 'function') {
      dateTime = getSelectedDateTime();
    }
    
    if (!brand || !areaType || !dateTime) {
      alert('请选择品牌、商圈类型和日期时间');
      return;
    }
    
    // 获取各种系数
    const dineUnitPrice = getBusinessCoefficientSafe(brand, areaType, '堂食客单价', 30);
    const deliveryUnitPrice = getBusinessCoefficientSafe(brand, areaType, '外送客单价', 25);
    const thirdPartyRatio = getBusinessCoefficientSafe(brand, areaType, '第三方外送占比参考', 0.8);
    const onlineOrderRatio = getBusinessCoefficientSafe(brand, areaType, '线上点餐占比参考', 0.5);
    
    // 获取时段系数
    let timePeriodCoefficient = 1;
    if (typeof getTimePeriodCoefficient === 'function') {
      const tpc = getTimePeriodCoefficient(dateTime, areaType);
      timePeriodCoefficient = parseNumberOrPercent(tpc, 1);
    }
    
    // 获取周系数
    let weekCoefficient = 1;
    if (typeof getWeekCoefficient === 'function') {
      const wc = getWeekCoefficient(dateTime, brand, areaType);
      weekCoefficient = parseNumberOrPercent(wc, 1);
    }
    
    // 获取月系数
    let monthCoefficient = 1;
    if (typeof getMonthCoefficient === 'function') {
      const mc = getMonthCoefficient(dateTime, brand, areaType);
      monthCoefficient = parseNumberOrPercent(mc, 1);
    }
    
    // 获取月天数
    const daysInMonth = new Date(dateTime.getFullYear(), dateTime.getMonth() + 1, 0).getDate();
    
    // 计算堂食当日单量
    const dineTotal = mobileOrders + otherOrders;
    
    // 计算外送当日单量
    const deliveryDailyTotal = meituanOrders + elemeOrders;
    
    // 计算外送月单量预估
    // 外送月单量预估 = (美团单量 + 饿了么单量) / 第三方外送占比参考
    let deliveryTotal = 0;
    if (thirdPartyRatio && thirdPartyRatio > 0) {
      deliveryTotal = deliveryDailyTotal / thirdPartyRatio;
    } else {
      deliveryTotal = deliveryDailyTotal;
    }
    
    // 计算堂食月单量预估
    let dineMonthlyTotal = 0;
    
    // 安全检查，避免除以0
    const safeTimePeriodCoefficient = timePeriodCoefficient > 0 ? timePeriodCoefficient : 1;
    const safeWeekCoefficient = weekCoefficient > 0 ? weekCoefficient : 1;
    const safeMonthCoefficient = monthCoefficient > 0 ? monthCoefficient : 1;
    const safeOnlineOrderRatio = onlineOrderRatio > 0 ? onlineOrderRatio : 1;
    
    if (orderChannel === '现场编号') {
      // 堂食月单量预估 = 堂食当日单量 / 时段系数 / 周系数 / 7 * 月天数 / 月系数
      dineMonthlyTotal = dineTotal / safeTimePeriodCoefficient / safeWeekCoefficient / 7 * daysInMonth / safeMonthCoefficient;
    } else if (orderChannel === '手机点餐') {
      // 堂食月单量预估 = 堂食当日单量 / 线上点餐占比参考 / 时段系数 / 周系数 / 7 * 月天数 / 月系数
      dineMonthlyTotal = dineTotal / safeOnlineOrderRatio / safeTimePeriodCoefficient / safeWeekCoefficient / 7 * daysInMonth / safeMonthCoefficient;
    } else {
      // 默认情况，使用简单计算
      dineMonthlyTotal = dineTotal * daysInMonth;
    }
    
    // 计算堂食月营业额预估
    let dineRevenue = 0;
    if (orderChannel === '现场编号') {
      // 堂食月营业额预估 = 堂食客单价 * 堂食当日单量 / 时段系数 / 周系数 / 7 * 月天数 / 月系数
      dineRevenue = dineUnitPrice * dineTotal / safeTimePeriodCoefficient / safeWeekCoefficient / 7 * daysInMonth / safeMonthCoefficient;
    } else if (orderChannel === '手机点餐') {
      // 堂食月营业额预估 = 堂食客单价 * 堂食当日单量 / 线上点餐占比参考 / 时段系数 / 周系数 / 7 * 月天数 / 月系数
      dineRevenue = dineUnitPrice * dineTotal / safeOnlineOrderRatio / safeTimePeriodCoefficient / safeWeekCoefficient / 7 * daysInMonth / safeMonthCoefficient;
    } else {
      // 默认情况，使用简单计算
      dineRevenue = dineUnitPrice * dineTotal;
    }
    
    // 计算外送月营业额预估
    // 外送月营业额预估 = 外送客单价 * 外送月单量预估 / 月系数
    let deliveryRevenue = 0;
    if (monthCoefficient && monthCoefficient > 0) {
      deliveryRevenue = deliveryUnitPrice * deliveryTotal / monthCoefficient;
    } else {
      deliveryRevenue = deliveryUnitPrice * deliveryTotal;
    }
    
    // 计算月总营业额预估
    const totalRevenue = dineRevenue + deliveryRevenue;
    
    // 计算外送月营业额占比
    let deliveryMonthRatio = 0;
    if (totalRevenue > 0) {
      deliveryMonthRatio = deliveryRevenue / totalRevenue;
    }
    
    // 获取外送年度占比参考值
    const deliveryYearRatio = getBusinessCoefficientSafe(brand, areaType, '外送年度占比参考值', 0.5);
    
    // 更新显示
    document.getElementById('dineRevenue').innerText = formatNumber(Math.round(dineRevenue));
    document.getElementById('deliveryRevenue').innerText = formatNumber(Math.round(deliveryRevenue));
    document.getElementById('deliveryYearRatio').innerText = (deliveryYearRatio * 100).toFixed(0) + '%';
    
    // 如果存在总营业额显示元素，则更新
    const totalRevenueElement = document.getElementById('totalRevenue');
    if (totalRevenueElement) {
      totalRevenueElement.innerText = formatNumber(Math.round(totalRevenue));
    }
    
    // 记录计算过程，便于调试
    console.log('计算参数:', {
      dineUnitPrice,
      deliveryUnitPrice,
      thirdPartyRatio,
      onlineOrderRatio,
      timePeriodCoefficient,
      weekCoefficient,
      monthCoefficient,
      daysInMonth,
      orderChannel
    });
    
    console.log('计算结果:', {
      dineRevenue,
      deliveryRevenue,
      totalRevenue,
      deliveryYearRatio: (deliveryYearRatio * 100).toFixed(0) + '%'
    });
    
  } catch (error) {
    console.error('计算测算结果时出错:', error);
    alert('计算出错: ' + error.message);
  }
}

/**
 * 计算堂食月营业额
 * @param {number} dineUnitPrice - 堂食客单价
 * @param {number} dineTotal - 堂食当日单量
 * @param {number} timePeriodCoefficient - 时段系数
 * @param {number} weekCoefficient - 周系数
 * @param {number} daysInMonth - 月天数
 * @param {number} monthCoefficient - 月系数
 * @param {number} onlineOrderRatio - 线上点餐占比参考（如果不需要则传入1）
 * @returns {number} 堂食月营业额
 */
function calculateDineRevenue(
  dineUnitPrice, 
  dineTotal, 
  timePeriodCoefficient, 
  weekCoefficient, 
  daysInMonth, 
  monthCoefficient, 
  onlineOrderRatio
) {
  // 安全检查，避免除以0
  const safeTimePeriodCoefficient = timePeriodCoefficient > 0 ? timePeriodCoefficient : 1;
  const safeWeekCoefficient = weekCoefficient > 0 ? weekCoefficient : 1;
  const safeMonthCoefficient = monthCoefficient > 0 ? monthCoefficient : 1;
  const safeOnlineOrderRatio = onlineOrderRatio > 0 ? onlineOrderRatio : 1;
  
  // 计算公式
  return dineUnitPrice * dineTotal / safeOnlineOrderRatio / safeTimePeriodCoefficient / safeWeekCoefficient / 7 * daysInMonth / safeMonthCoefficient;
}

/**
 * 安全获取经营系数，如果获取失败则返回默认值
 * @param {string} brand - 品牌
 * @param {string} areaType - 商圈类型
 * @param {string} field - 经营系数字段名
 * @param {number} defaultValue - 默认值
 * @returns {number} 经营系数值
 */
function getBusinessCoefficientSafe(brand, areaType, field, defaultValue) {
  try {
    if (typeof getBusinessCoefficient === 'function') {
      const value = getBusinessCoefficient(brand, areaType, field);
      if (value !== undefined && value !== null && value !== '') {
        return parseNumberOrPercent(value, defaultValue);
      }
    }
  } catch (error) {
    console.warn(`获取${field}时出错:`, error);
  }
  return defaultValue;
}

/**
 * 解析数字或百分比字符串
 * @param {string|number} value - 要解析的值
 * @param {number} defaultValue - 默认值
 * @returns {number} 解析后的数值
 */
function parseNumberOrPercent(value, defaultValue) {
  if (value === undefined || value === null || value === '') {
    return defaultValue;
  }
  
  if (typeof value === 'number') {
    return value;
  }
  
  // 尝试解析为数字
  if (typeof value === 'string') {
    // 移除所有空白字符
    const trimmedValue = value.replace(/\s/g, '');
    
    // 检查是否为百分比格式
    if (trimmedValue.endsWith('%')) {
      const percentValue = parseFloat(trimmedValue) / 100;
      return isNaN(percentValue) ? defaultValue : percentValue;
    }
    
    // 尝试直接解析为数字
    const numericValue = parseFloat(trimmedValue);
    return isNaN(numericValue) ? defaultValue : numericValue;
  }
  
  return defaultValue;
}

/**
 * 格式化数字，添加千分位分隔符
 * @param {string|number} value - 要格式化的数值
 * @returns {string} 格式化后的字符串
 */
function formatNumber(value) {
  if (value === undefined || value === null || value === '') {
    return '--';
  }
  
  // 确保value是字符串
  const strValue = String(value);
  
  // 检查是否有小数点
  const parts = strValue.split('.');
  
  // 对整数部分添加千分位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  
  // 重新组合整数和小数部分
  return parts.join('.');
}

// 导出函数
window.initCalculationResult = initCalculationResult;
window.calculateResults = calculateResults;