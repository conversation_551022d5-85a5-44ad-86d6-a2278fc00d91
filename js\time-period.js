/**
 * time-period.js
 * 版本: 1.1.1
 * 功能: 根据用户选择的时间计算当前时段和时段系数
 * 更新: 
 * - 1.1.0: 添加时段系数功能
 * - 1.1.1: 修复变量共享问题
 */

// 存储时段数据
let timePeriodData = [];
// 存储列标题与索引的映射
let columnMapping = {};

/**
 * 初始化时段系数功能
 */
function initTimePeriod() {
  // 监听日期时间变化
  document.addEventListener('datetimeChanged', function(e) {
    if (e.detail && e.detail.dateTime) {
      updateTimePeriod(e.detail.dateTime);
    }
  });
  
  // 监听商圈类型变化
  const areaTypeSelect = document.getElementById('areaType');
  if (areaTypeSelect) {
    areaTypeSelect.addEventListener('change', function() {
      // 如果已经有日期时间，则更新时段系数
      if (typeof getSelectedDateTime === 'function') {
        const dateTime = getSelectedDateTime();
        if (dateTime) {
          updateTimePeriod(dateTime);
        }
      }
    });
  }
}

/**
 * 处理Excel文件中的时段系数数据
 * @param {Object} workbook - XLSX工作簿对象
 * @returns {boolean} 是否成功处理
 */
function processTimePeriodData(workbook) {
  try {
    // 查找"时段系数"工作表
    const timePeriodSheet = workbook.Sheets['时段系数'];
    if (!timePeriodSheet) {
      console.warn('未找到"时段系数"工作表');
      return false;
    }
    
    // 获取工作表范围
    const range = XLSX.utils.decode_range(timePeriodSheet['!ref']);
    
    // 获取列标题（第一行）
    columnMapping = {};
    for (let col = 0; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
      const cell = timePeriodSheet[cellAddress];
      if (cell && cell.v) {
        columnMapping[cell.v] = col;
      }
    }
    
    // 转换为JSON
    timePeriodData = XLSX.utils.sheet_to_json(timePeriodSheet);
    
    // 确保数据格式正确
    if (!timePeriodData.length || !timePeriodData[0]['时间'] || !timePeriodData[0]['当前时段']) {
      console.warn('时段系数数据格式不正确');
      return false;
    }
    
    // 保留原始工作表数据，用于后续查询特定列的值
    timePeriodData.sheet = timePeriodSheet;
    timePeriodData.range = range;
    
    return true;
  } catch (error) {
    console.error('处理时段系数数据时出错:', error);
    return false;
  }
}

/**
 * 更新当前时段显示
 * @param {Date} dateTime - 日期时间对象
 */
function updateTimePeriod(dateTime) {
  if (!timePeriodData.length) return;
  
  // 获取当前时间（只关注小时和分钟）
  const hours = dateTime.getHours();
  const minutes = dateTime.getMinutes();
  const currentTime = hours * 60 + minutes; // 转换为分钟计数
  
  // 查找匹配的时段
  let currentPeriod = '';
  let rowIndex = -1;
  
  for (let i = 0; i < timePeriodData.length; i++) {
    const row = timePeriodData[i];
    const timeRange = row['时间'];
    if (!timeRange) continue;
    
    // 解析时间范围，格式如 "5:00-10:30"
    const [startStr, endStr] = timeRange.split('-');
    if (!startStr || !endStr) continue;
    
    const [startHours, startMinutes] = startStr.split(':').map(Number);
    const [endHours, endMinutes] = endStr.split(':').map(Number);
    
    if (isNaN(startHours) || isNaN(startMinutes) || isNaN(endHours) || isNaN(endMinutes)) continue;
    
    const startTime = startHours * 60 + startMinutes;
    let endTime = endHours * 60 + endMinutes;
    
    // 处理跨天的情况，如 "24:00-5:00"
    if (endTime < startTime) {
      // 如果当前时间大于等于开始时间或小于等于结束时间，则在该时段内
      if (currentTime >= startTime || currentTime <= endTime) {
        currentPeriod = row['当前时段'];
        rowIndex = i;
        break;
      }
    } else {
      // 正常情况，如果当前时间在开始和结束之间，则在该时段内
      if (currentTime >= startTime && currentTime <= endTime) {
        currentPeriod = row['当前时段'];
        rowIndex = i;
        break;
      }
    }
  }
  
  // 更新时段显示
  const timePeriodElement = document.getElementById('currentTimePeriod');
  if (timePeriodElement) {
    timePeriodElement.textContent = currentPeriod || '--';
  }
  
  // 更新时段系数
  updateTimePeriodCoefficient(rowIndex);
}

/**
 * 更新时段系数显示
 * @param {number} rowIndex - 时段数据行索引
 */
function updateTimePeriodCoefficient(rowIndex) {
  const coefficientElement = document.getElementById('timePeriodCoefficient');
  if (!coefficientElement) return;
  
  // 获取当前选中的商圈类型
  const currentAreaType = document.getElementById('areaType').value;
  
  // 如果没有找到匹配的时段或没有选择商圈类型
  if (rowIndex === -1 || !currentAreaType) {
    coefficientElement.textContent = '--';
    return;
  }
  
  // 如果没有列映射或没有原始工作表数据
  if (!columnMapping || !timePeriodData.sheet || !timePeriodData.range) {
    coefficientElement.textContent = '--';
    return;
  }
  
  // 获取商圈类型对应的列索引
  const colIndex = columnMapping[currentAreaType];
  if (colIndex === undefined) {
    coefficientElement.textContent = '--';
    return;
  }
  
  // 获取对应单元格的值
  const cellAddress = XLSX.utils.encode_cell({ r: rowIndex + 1, c: colIndex });
  const cell = timePeriodData.sheet[cellAddress];
  
  // 显示系数值
  if (cell && cell.v !== undefined) {
    // 如果是百分比，转换为更友好的显示格式
    if (typeof cell.v === 'number') {
      coefficientElement.textContent = (cell.v * 100).toFixed(0) + '%';
    } else {
      coefficientElement.textContent = cell.v;
    }
  } else {
    coefficientElement.textContent = '--';
  }
}

/**
 * 获取当前时段
 * @param {Date} dateTime - 日期时间对象
 * @returns {string} 当前时段
 */
function getCurrentTimePeriod(dateTime) {
  if (!dateTime || !timePeriodData.length) return '';
  
  // 获取当前时间（只关注小时和分钟）
  const hours = dateTime.getHours();
  const minutes = dateTime.getMinutes();
  const currentTime = hours * 60 + minutes; // 转换为分钟计数
  
  // 查找匹配的时段
  for (const row of timePeriodData) {
    const timeRange = row['时间'];
    if (!timeRange) continue;
    
    // 解析时间范围，格式如 "5:00-10:30"
    const [startStr, endStr] = timeRange.split('-');
    if (!startStr || !endStr) continue;
    
    const [startHours, startMinutes] = startStr.split(':').map(Number);
    const [endHours, endMinutes] = endStr.split(':').map(Number);
    
    if (isNaN(startHours) || isNaN(startMinutes) || isNaN(endHours) || isNaN(endMinutes)) continue;
    
    const startTime = startHours * 60 + startMinutes;
    let endTime = endHours * 60 + endMinutes;
    
    // 处理跨天的情况，如 "24:00-5:00"
    if (endTime < startTime) {
      // 如果当前时间大于等于开始时间或小于等于结束时间，则在该时段内
      if (currentTime >= startTime || currentTime <= endTime) {
        return row['当前时段'];
      }
    } else {
      // 正常情况，如果当前时间在开始和结束之间，则在该时段内
      if (currentTime >= startTime && currentTime <= endTime) {
        return row['当前时段'];
      }
    }
  }
  
  return '';
}

/**
 * 获取当前时段系数
 * @param {Date} dateTime - 日期时间对象
 * @param {string} areaType - 商圈类型
 * @returns {number|string} 时段系数
 */
function getTimePeriodCoefficient(dateTime, areaType) {
  if (!dateTime || !areaType || !timePeriodData.length || !columnMapping || !timePeriodData.sheet) return '';
  
  // 获取当前时间（只关注小时和分钟）
  const hours = dateTime.getHours();
  const minutes = dateTime.getMinutes();
  const currentTime = hours * 60 + minutes; // 转换为分钟计数
  
  // 查找匹配的时段
  let rowIndex = -1;
  for (let i = 0; i < timePeriodData.length; i++) {
    const row = timePeriodData[i];
    const timeRange = row['时间'];
    if (!timeRange) continue;
    
    // 解析时间范围
    const [startStr, endStr] = timeRange.split('-');
    if (!startStr || !endStr) continue;
    
    const [startHours, startMinutes] = startStr.split(':').map(Number);
    const [endHours, endMinutes] = endStr.split(':').map(Number);
    
    if (isNaN(startHours) || isNaN(startMinutes) || isNaN(endHours) || isNaN(endMinutes)) continue;
    
    const startTime = startHours * 60 + startMinutes;
    let endTime = endHours * 60 + endMinutes;
    
    // 处理跨天的情况
    if (endTime < startTime) {
      if (currentTime >= startTime || currentTime <= endTime) {
        rowIndex = i;
        break;
      }
    } else {
      if (currentTime >= startTime && currentTime <= endTime) {
        rowIndex = i;
        break;
      }
    }
  }
  
  if (rowIndex === -1) return '';
  
  // 获取商圈类型对应的列索引
  const colIndex = columnMapping[areaType];
  if (colIndex === undefined) return '';
  
  // 获取对应单元格的值
  const cellAddress = XLSX.utils.encode_cell({ r: rowIndex + 1, c: colIndex });
  const cell = timePeriodData.sheet[cellAddress];
  
  return cell ? cell.v : '';
}

// 导出函数
window.initTimePeriod = initTimePeriod;
window.processTimePeriodData = processTimePeriodData;
window.getCurrentTimePeriod = getCurrentTimePeriod;
window.getTimePeriodCoefficient = getTimePeriodCoefficient; 